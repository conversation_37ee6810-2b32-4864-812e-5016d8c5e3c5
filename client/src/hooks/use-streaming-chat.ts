import { useState, useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import type { Message } from '@shared/schema';

// API base path for production
const API_BASE = import.meta.env.PROD ? "/jurbot-chat/api" : "/api";

interface StreamingChunk {
  type: 'chunk' | 'complete' | 'error' | 'end';
  content?: string;
  fullContent?: string;
  citations?: any[];
  userMessage?: Message;
  aiMessage?: Message;
  error?: string;
}

interface UseStreamingChatOptions {
  sessionId: string;
  onChunk?: (chunk: StreamingChunk) => void;
  onComplete?: (data: { userMessage: Message; aiMessage: Message; citations?: any[] }) => void;
  onError?: (error: string) => void;
}

export function useStreamingChat({ sessionId, onChunk, onComplete, onError }: UseStreamingChatOptions) {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingContent, setStreamingContent] = useState('');
  const [citations, setCitations] = useState<any[]>([]);
  const abortControllerRef = useRef<AbortController | null>(null);
  const queryClient = useQueryClient();

  const sendStreamingMessage = useCallback(async (content: string) => {
    if (isStreaming) return;

    setIsStreaming(true);
    setStreamingContent('');
    setCitations([]);

    // Create abort controller for this request
    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch(`${API_BASE}/chat-sessions/${sessionId}/messages/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          role: 'user',
        }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;
          if (!line.startsWith('data: ')) continue;

          try {
            const data: StreamingChunk = JSON.parse(line.slice(6));
            
            if (data.type === 'chunk') {
              setStreamingContent(data.fullContent || '');
              if (data.citations) {
                setCitations(data.citations);
              }
              onChunk?.(data);
            } else if (data.type === 'complete') {
              if (data.userMessage && data.aiMessage) {
                // Invalidate queries to refresh the message list
                queryClient.invalidateQueries({ 
                  queryKey: [`${API_BASE}/chat-sessions`, sessionId, "messages"] 
                });
                onComplete?.({
                  userMessage: data.userMessage,
                  aiMessage: data.aiMessage,
                  citations: data.citations
                });
              }
            } else if (data.type === 'error') {
              onError?.(data.error || 'Unknown error occurred');
            } else if (data.type === 'end') {
              break;
            }
          } catch (e) {
            console.error('Error parsing streaming data:', e);
          }
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Streaming request was aborted');
      } else {
        console.error('Streaming error:', error);
        onError?.(error instanceof Error ? error.message : 'Unknown error occurred');
      }
    } finally {
      setIsStreaming(false);
      setStreamingContent('');
      setCitations([]);
      abortControllerRef.current = null;
    }
  }, [sessionId, isStreaming, onChunk, onComplete, onError, queryClient]);

  const stopStreaming = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  return {
    sendStreamingMessage,
    stopStreaming,
    isStreaming,
    streamingContent,
    citations,
  };
}
