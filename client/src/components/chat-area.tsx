import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON>ting<PERSON>, Download, Fan, Calendar, <PERSON>u, Co<PERSON>, ThumbsUp, ThumbsDown, Info } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { MessageInput } from "@/components/message-input";
import { WebhookConfig } from "@/components/webhook-config";
import { useChat } from "@/hooks/use-chat";
import { useToast } from "@/hooks/use-toast";
import { format, isAfter, isBefore, startOfDay, endOfDay } from "date-fns";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { Input } from "@/components/ui/input";
import { useIsMobile } from "@/hooks/use-mobile";
import { useStreamingChat } from "@/hooks/use-streaming-chat";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

// Types for RAGFlow citations
interface Citation {
  id: string;
  content: string;
  document_id: string;
  document_name: string;
  similarity?: number;
  position?: string[];
}

interface MessageWithCitations {
  id: number;
  content: string;
  role: string;
  timestamp: string;
  citations?: Citation[];
}

interface ChatAreaProps {
  sessionId: string | null;
  startDate?: string;
  endDate?: string;
  showWebhookConfig?: boolean;
  onCloseWebhookConfig?: () => void;
  onMenuClick?: () => void;
}

// Citation component to display a reference marker with hover details
function CitationReference({ citation, index }: { citation: Citation; index: number }) {
  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <button 
          className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-primary/10 text-primary text-xs font-medium hover:bg-primary/20 transition-colors"
          aria-label={`Citation ${index + 1}: ${citation.document_name}`}
        >
          {index + 1}
        </button>
      </HoverCardTrigger>
      <HoverCardContent className="w-80 p-3 text-sm">
        <div className="space-y-2">
          <div className="font-medium">{citation.document_name}</div>
          <div className="text-xs text-muted-foreground line-clamp-4">{citation.content}</div>
          {citation.similarity && (
            <div className="text-xs text-muted-foreground">
              Relevance: {Math.round(citation.similarity * 100)}%
            </div>
          )}
        </div>
      </HoverCardContent>
    </HoverCard>
  );
}

// Component to display a list of citations
function CitationsList({ citations }: { citations: Citation[] }) {
  if (!citations || citations.length === 0) return null;

  const handleDocumentClick = (citation: Citation) => {
    // For now, we'll just show an alert with document info
    // In a real implementation, this would open the document viewer
    alert(`Opening document: ${citation.document_name}\nDocument ID: ${citation.document_id}`);
  };

  return (
    <div className="mt-3 pt-3 border-t border-border">
      <h4 className="text-xs font-medium text-muted-foreground mb-2 flex items-center gap-1">
        <Info className="h-3 w-3" />
        Sources
      </h4>
      <div className="space-y-1">
        {citations.map((citation, index) => (
          <button
            key={citation.id || index}
            onClick={() => handleDocumentClick(citation)}
            className="w-full text-left text-xs hover:bg-muted/50 p-2 rounded transition-colors"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="font-medium">[{index + 1}]</span>
                <span className="text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-200 underline">
                  {citation.document_name}
                </span>
              </div>
              {citation.similarity && (
                <span className="opacity-70 text-xs">
                  {(citation.similarity * 100).toFixed(1)}% match
                </span>
              )}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
}

export function ChatArea({ 
  sessionId, 
  startDate = "", 
  endDate = "", 
  showWebhookConfig = false, 
  onCloseWebhookConfig,
  onMenuClick,
}: ChatAreaProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { messages, sendMessage, isLoading, clearMessages } = useChat(sessionId);
  const { toast } = useToast();
  const [typingIndicator, setTypingIndicator] = useState(false);
  const [feedback, setFeedback] = useState<{[key: string]: 'like' | 'dislike' | null}>({});
  const isMobile = useIsMobile();

  // Store citations for each message
  const [messageCitations, setMessageCitations] = useState<{[key: string]: Citation[]}>({});

  // Streaming state
  const [streamingMessage, setStreamingMessage] = useState<{id: string, content: string} | null>(null);

  // Streaming chat hook
  const { sendStreamingMessage, stopStreaming, isStreaming, streamingContent, citations: streamingCitations } = useStreamingChat({
    sessionId: sessionId || '',
    onChunk: (chunk) => {
      // Update streaming message content
      if (chunk.fullContent) {
        setStreamingMessage({
          id: 'streaming',
          content: chunk.fullContent
        });
      }
    },
    onComplete: (data) => {
      // Store citations for the completed message
      if (data.citations && data.citations.length > 0 && data.aiMessage) {
        setMessageCitations(prev => ({
          ...prev,
          [data.aiMessage.id]: data.citations as Citation[]
        }));
      }
      setStreamingMessage(null);
    },
    onError: (error) => {
      console.error("Streaming error:", error);
      setStreamingMessage(null);
      toast({
        title: "Error",
        description: error,
        variant: "destructive",
      });
    }
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isLoading || isStreaming) {
      setTypingIndicator(true);
    } else {
      setTypingIndicator(false);
    }
  }, [isLoading, isStreaming]);

  const handleSendMessage = async (content: string) => {
    if (!sessionId) return;

    // Try streaming first (for RAGFlow), fallback to regular send
    try {
      await sendStreamingMessage(content);
    } catch (error) {
      console.error("Streaming failed, falling back to regular send:", error);
      try {
        const response = await sendMessage(content);

        // Check if the response contains citations from RAGFlow
        if (response.citations && response.citations.length > 0 && response.aiMessage) {
          setMessageCitations(prev => ({
            ...prev,
            [response.aiMessage.id]: response.citations as Citation[]
          }));
        }
      } catch (fallbackError) {
        console.error("Error sending message:", fallbackError);
      }
    }
  };

  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content);
    toast({
      title: "Copied to clipboard",
      description: "The message has been copied.",
    });
  };

  const handleFeedback = (messageId: string, newFeedback: 'like' | 'dislike') => {
    setFeedback(prev => {
      const currentFeedback = prev[messageId];
      if (currentFeedback === newFeedback) {
        // If clicking the same button again, remove feedback
        const newFeedbackState = { ...prev };
        delete newFeedbackState[messageId];
        return newFeedbackState;
      }
      return { ...prev, [messageId]: newFeedback };
    });
  };

  const handleClearMessages = async () => {
    if (!sessionId) return;
    if (confirm("Are you sure you want to clear all messages?")) {
      await clearMessages();
      // Clear citations when messages are cleared
      setMessageCitations({});
    }
  };

  const handleDownloadChat = () => {
    if (!sessionId || messages.length === 0) return;
    
    const chatContent = filteredMessages.map(message => {
      const timestamp = format(new Date(message.timestamp), "yyyy-MM-dd HH:mm:ss");
      const role = message.role === "user" ? "User" : "AI Assistant";
      return `[${timestamp}] ${role}: ${message.content}`;
    }).join('\n\n');

    const blob = new Blob([chatContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-session-${sessionId}-${format(new Date(), "yyyy-MM-dd")}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Filter messages based on date range
  const filteredMessages = messages.filter(message => {
    if (!startDate && !endDate) return true;
    
    const messageDate = new Date(message.timestamp);
    const start = startDate ? startOfDay(new Date(startDate)) : null;
    const end = endDate ? endOfDay(new Date(endDate)) : null;
    
    if (start && end) {
      return !isBefore(messageDate, start) && !isAfter(messageDate, end);
    } else if (start) {
      return !isBefore(messageDate, start);
    } else if (end) {
      return !isAfter(messageDate, end);
    }
    
    return true;
  });

  if (!sessionId) {
    return (
      <div className="flex-1 flex items-center justify-center chat-area-bg">
        <div className="text-center">
          <div className="text-4xl mb-6">🤖</div>
          <h2 className="text-xl font-bold mb-3 text-foreground">Hey, I'm Jurbot</h2>
          <p className="text-xl text-muted-foreground">Please create a new chat</p>
        </div>
      </div>
    );
  }

  // Function to replace [ID:n] markers with info icons
  const replaceCitationMarkers = (content: string, citations: Citation[] = []) => {
    if (!citations || citations.length === 0) return content;

    // Replace [ID:n] with info icons
    return content.replace(/\[ID:(\d+)\]/g, (match, index) => {
      const citationIndex = parseInt(index);
      const citation = citations[citationIndex];

      if (!citation) return match;

      // Return a placeholder that we'll replace with React components
      return `__CITATION_${citationIndex}__`;
    });
  };

  // Function to render content with citation components
  const renderContentWithCitations = (content: string, citations: Citation[] = []) => {
    if (!citations || citations.length === 0) return content;

    // Replace [ID:n] with info icons
    return content.replace(/\[ID:(\d+)\]/g, (match, index) => {
      const citationIndex = parseInt(index);
      const citation = citations[citationIndex];

      if (!citation) return match;

      // Return a special marker that ReactMarkdown will handle
      return `<span class="citation-marker" data-citation-index="${citationIndex}">[i]</span>`;
    });
  };

  // Function to process thinking tags
  const processThinkingTags = (content: string) => {
    return content.replace(/<think>([\s\S]*?)<\/think>/g, (match, thinkContent) => {
      return `<div class="think-section">${thinkContent}</div>`;
    });
  };

  // Function to render message content with citation references
  const renderMessageContent = (message: MessageWithCitations) => {
    const citations = messageCitations[message.id];

    if (message.role === "assistant") {
      // Process the content with thinking tags and citations
      const processedContent = processThinkingTags(message.content);

      return (
        <>
          <div className="prose prose-sm max-w-none dark:prose-invert">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
              components={{
                p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                ul: ({ children }) => <ul className="mb-2 last:mb-0 ml-4">{children}</ul>,
                ol: ({ children }) => <ol className="mb-2 last:mb-0 ml-4">{children}</ol>,
                li: ({ children }) => <li className="mb-1">{children}</li>,
                code: ({ children }) => <code className="bg-black/20 dark:bg-white/20 px-1 py-0.5 rounded text-sm">{children}</code>,
                pre: ({ children }) => <pre className="bg-black/20 dark:bg-white/20 p-2 rounded text-sm overflow-x-auto">{children}</pre>,
                blockquote: ({ children }) => <blockquote className="border-l-2 border-black/30 dark:border-white/30 pl-4 italic">{children}</blockquote>,
                a: ({ href, children }) => <a href={href} className="text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-200 underline break-all" target="_blank" rel="noopener noreferrer">{children}</a>,
                h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                h2: ({ children }) => <h2 className="text-base font-bold mb-2">{children}</h2>,
                h3: ({ children }) => <h3 className="text-sm font-bold mb-1">{children}</h3>,
                div: ({ children, className }) => {
                  if (className === 'think-section') {
                    return (
                      <div className="think-section pl-3 text-gray-500 dark:text-gray-400 border-l-2 border-gray-300 dark:border-gray-600 mb-3 text-sm">
                        {children}
                      </div>
                    );
                  }
                  return <div className={className}>{children}</div>;
                },
                span: ({ children, className, ...props }) => {
                  if (className === 'citation-marker') {
                    const citationIndex = parseInt((props as any)['data-citation-index'] || '0');
                    const citation = citations?.[citationIndex];

                    if (!citation) return <span>{children}</span>;

                    return (
                      <Popover>
                        <PopoverTrigger asChild>
                          <button className="inline-flex items-center justify-center w-4 h-4 mx-1 text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-200">
                            <Info className="w-3 h-3" />
                          </button>
                        </PopoverTrigger>
                        <PopoverContent className="max-w-md p-3">
                          <div className="space-y-2">
                            <div
                              className="text-sm text-gray-700 dark:text-gray-300 max-h-32 overflow-y-auto"
                              dangerouslySetInnerHTML={{ __html: citation.content }}
                            />
                            <div className="text-xs text-gray-500 dark:text-gray-400 border-t pt-2">
                              <strong>Source:</strong> {citation.document_name}
                              {citation.similarity && (
                                <div><strong>Similarity:</strong> {(citation.similarity * 100).toFixed(1)}%</div>
                              )}
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    );
                  }
                  return <span className={className} {...props}>{children}</span>;
                },
              }}
            >
              {renderContentWithCitations(processedContent, citations)}
            </ReactMarkdown>
          </div>

          {/* Display the list of citations below the message */}
          {citations && citations.length > 0 && <CitationsList citations={citations} />}
        </>
      );
    } else {
      return <p className="whitespace-pre-wrap break-words">{message.content}</p>;
    }
  };

  return (
    <div className="flex-1 flex flex-col chat-area-bg">
      {/* Header */}
      <div className="p-4 border-b border-border bg-background/95 backdrop-blur">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {isMobile && (
              <Button onClick={onMenuClick} size="sm" variant="ghost">
                <Menu className="h-6 w-6" />
              </Button>
            )}
            <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
              <Bot className="h-4 w-4 text-primary-foreground" />
            </div>
            <div>
              <h2 className="text-lg font-semibold">AI Assistant</h2>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>{messages.length} messages</span>
                {(startDate || endDate) && (
                  <>
                    <span>•</span>
                    <span>Filtered</span>
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={handleDownloadChat}
              size="sm"
              variant="outline"
              disabled={messages.length === 0}
            >
              <Download className="h-4 w-4" />
            </Button>
            <Button
              onClick={handleClearMessages}
              size="sm"
              variant="outline"
            >
              <Fan className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {filteredMessages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
          >
            <div className="max-w-[70%] lg:max-w-[60%] xl:max-w-[50%]">
              <div
                className={`rounded-2xl p-4 shadow-lg break-words ${
                  message.role === "user"
                    ? "chat-user-bubble rounded-br-md"
                    : "chat-ai-bubble rounded-bl-md"
                }`}
              >
                {renderMessageContent(message as MessageWithCitations)}
              </div>
              <div
                className={`flex items-center gap-2 mt-2 text-xs text-muted-foreground ${
                  message.role === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <span>{format(new Date(message.timestamp), "h:mm a")}</span>
                {message.role === 'assistant' && (
                  <div className="flex items-center gap-1">
                    <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => handleFeedback(String(message.id), 'dislike')}>
                      <ThumbsDown className={`h-4 w-4 ${feedback[message.id] === 'dislike' ? 'text-red-500' : ''}`} />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => handleFeedback(String(message.id), 'like')}>
                      <ThumbsUp className={`h-4 w-4 ${feedback[message.id] === 'like' ? 'text-blue-500' : ''}`} />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => handleCopy(message.content)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}

        {/* Streaming message */}
        {streamingMessage && (
          <div className="flex justify-start">
            <div className="max-w-[70%] lg:max-w-[60%] xl:max-w-[50%]">
              <div className="chat-ai-bubble rounded-2xl rounded-bl-md p-4 shadow-lg break-words">
                {(() => {
                  // Create a temporary message with streaming citations
                  const tempMessage = {
                    id: parseInt(streamingMessage.id),
                    content: streamingMessage.content,
                    role: "assistant",
                    timestamp: new Date().toISOString(),
                    sessionId: sessionId || ''
                  } as MessageWithCitations;

                  // Add streaming citations to the temporary citations store
                  if (streamingCitations && streamingCitations.length > 0) {
                    setMessageCitations(prev => ({
                      ...prev,
                      [streamingMessage.id]: streamingCitations
                    }));
                  }

                  return renderMessageContent(tempMessage);
                })()}
                <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                  <div className="flex space-x-1">
                    <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse"></div>
                    <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse" style={{ animationDelay: "0.1s" }}></div>
                    <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse" style={{ animationDelay: "0.2s" }}></div>
                  </div>
                  <span>Streaming...</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {typingIndicator && !streamingMessage && (
          <div className="flex justify-start">
            <div className="max-w-xs lg:max-w-md xl:max-w-lg">
              <div className="chat-ai-bubble rounded-2xl rounded-bl-md p-4 shadow-lg">
                <div className="flex items-center gap-2">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-current rounded-full animate-bounce opacity-50"></div>
                    <div className="w-2 h-2 bg-current rounded-full animate-bounce opacity-50" style={{ animationDelay: "0.1s" }}></div>
                    <div className="w-2 h-2 bg-current rounded-full animate-bounce opacity-50" style={{ animationDelay: "0.2s" }}></div>
                  </div>
                  <span className="text-xs opacity-60">AI is typing...</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <MessageInput
        onSendMessage={handleSendMessage}
        isLoading={isLoading}
        sessionId={sessionId}
      />

      {showWebhookConfig && onCloseWebhookConfig && (
        <WebhookConfig onClose={onCloseWebhookConfig} />
      )}
    </div>
  );
}
