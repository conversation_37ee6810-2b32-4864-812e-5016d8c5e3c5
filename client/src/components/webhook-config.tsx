import { useState } from "react";
import { Set<PERSON><PERSON>, TestTube, CheckCircle, AlertCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useEffect } from "react";

interface WebhookConfigProps {
  onClose: () => void;
}

export function WebhookConfig({ onClose }: WebhookConfigProps) {
  /* ------------------------------------------------------------------
   * Local state
   * ------------------------------------------------------------------ */
  const [engine, setEngine] = useState<"n8n" | "ragflow">("n8n");
  const [webhookUrl, setWebhookUrl] = useState("");
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [isTestingWebhook, setIsTestingWebhook] = useState(false);
  const [isSavingRagflow, setIsSavingRagflow] = useState(false);
  const [isLoadingConfig, setIsLoadingConfig] = useState(true);
  const [ragflowApiKey, setRagflowApiKey] = useState("");
  const [ragflowChatId, setRagflowChatId] = useState("");
  const [ragflowServer, setRagflowServer] = useState("");
  const { toast } = useToast();

  /* ------------------------------------------------------------------
   * Load existing configuration on mount
   * ------------------------------------------------------------------ */
  useEffect(() => {
    (async () => {
      try {
        const res = await fetch("/api/config");
        const data = await res.json();
        if (data.chatEngine === "ragflow") {
          setEngine("ragflow");
        }
        if (data.webhookUrl) setWebhookUrl(data.webhookUrl);
        // Optional: backend could return ragflowConfig - handle if present
        if (data.ragflowConfig) {
          const cfg = data.ragflowConfig;
          if (cfg.apiKey) setRagflowApiKey(cfg.apiKey);
          if (cfg.chatId) setRagflowChatId(cfg.chatId);
          if (cfg.serverAddress) setRagflowServer(cfg.serverAddress);
        }
      } catch (e) {
        console.error("Failed to load config", e);
        toast({
          title: "Error",
          description: "Failed to load configuration",
          variant: "destructive",
        });
      } finally {
        setIsLoadingConfig(false);
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleTestWebhook = async () => {
    if (engine !== "n8n") return;
    if (!webhookUrl.trim()) {
      toast({
        title: "Error",
        description: "Please enter a webhook URL first",
        variant: "destructive",
      });
      return;
    }

    setIsTestingWebhook(true);
    setTestResult(null);

    try {
      const response = await fetch("/api/test-webhook", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ webhookUrl }),
      });

      const data = await response.json();
      
      setTestResult({
        success: data.success,
        message: data.success ? "Webhook test successful!" : data.error || "Webhook test failed",
      });

      if (data.success) {
        toast({
          title: "Success",
          description: "Webhook is working correctly!",
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: "Failed to test webhook: " + (error instanceof Error ? error.message : "Unknown error"),
      });
    } finally {
      setIsTestingWebhook(false);
    }
  };

  const handleSaveRagflow = () => {
    if (!ragflowApiKey || !ragflowChatId || !ragflowServer) {
      toast({
        title: "Error",
        description: "Please fill in all RAGFlow fields",
        variant: "destructive",
      });
      return;
    }

    (async () => {
      setIsSavingRagflow(true);
      try {
        const res = await fetch("/api/ragflow-config", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            apiKey: ragflowApiKey,
            chatId: ragflowChatId,
            serverAddress: ragflowServer,
            enabled: true,
          }),
        });
        const data = await res.json();
        if (data.success) {
          toast({ title: "Success", description: data.message || "RAGFlow settings saved" });
          onClose();
        } else {
          throw new Error(data.error || "Unknown error");
        }
      } catch (e) {
        console.error(e);
        toast({
          title: "Error",
          description:
            "Failed to save RAGFlow settings: " + (e instanceof Error ? e.message : "Unknown error"),
          variant: "destructive",
        });
      } finally {
        setIsSavingRagflow(false);
      }
    })();
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Webhook Configuration
          </CardTitle>
          <CardDescription>
            Configure your n8n webhook URL to connect the AI assistant
          </CardDescription>
          <div className="mt-4">
            <Label>Chat Engine</Label>
            <Select value={engine} onValueChange={(val) => setEngine(val as "n8n" | "ragflow")}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Choose engine" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="n8n">n8n Webhook</SelectItem>
                <SelectItem value="ragflow">RAGFlow</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {engine === "n8n" && (
            <>
              <div className="space-y-2">
                <Label htmlFor="webhook-url">N8N Webhook URL</Label>
                <Input
                  disabled={isLoadingConfig}
                  id="webhook-url"
                  type="url"
                  placeholder="https://your-n8n-instance.com/webhook/your-webhook-id"
                  value={webhookUrl}
                  onChange={(e) => setWebhookUrl(e.target.value)}
                />
              </div>

              {testResult && (
                <Alert className={testResult.success ? "border-green-500" : "border-red-500"}>
                  {testResult.success ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                  <AlertDescription>{testResult.message}</AlertDescription>
                </Alert>
              )}
            </>
          )}

          {engine === "ragflow" && (
            <>
              <div className="space-y-2">
                <Label htmlFor="rf-api-key">RAGFlow API Key</Label>
                <Input
                  disabled={isLoadingConfig}
                  id="rf-api-key"
                  type="text"
                  placeholder="ragflow-xxxxxxxx"
                  value={ragflowApiKey}
                  onChange={(e) => setRagflowApiKey(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="rf-chat-id">RAGFlow Chat ID</Label>
                <Input
                  disabled={isLoadingConfig}
                  id="rf-chat-id"
                  type="text"
                  placeholder="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                  value={ragflowChatId}
                  onChange={(e) => setRagflowChatId(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="rf-server">RAGFlow Server Address</Label>
                <Input
                  disabled={isLoadingConfig}
                  id="rf-server"
                  type="url"
                  placeholder="http://your-ragflow-server"
                  value={ragflowServer}
                  onChange={(e) => setRagflowServer(e.target.value)}
                />
              </div>
            </>
          )}

          <div className="flex gap-2">
            {engine === "n8n" && (
              <Button
                onClick={handleTestWebhook}
                disabled={isTestingWebhook}
                variant="outline"
                className="flex-1"
              >
                <TestTube className="h-4 w-4 mr-2" />
                {isTestingWebhook ? "Testing..." : "Test Webhook"}
              </Button>
            )}
            {engine === "ragflow" && (
              <Button onClick={handleSaveRagflow} variant="outline" className="flex-1">
                {isSavingRagflow ? "Saving..." : "Save Settings"}
              </Button>
            )}
            <Button onClick={onClose} variant="ghost">
              Close
            </Button>
          </div>

          <div className="text-sm text-muted-foreground">
            <p className="font-medium mb-1">Instructions:</p>
            <ol className="list-decimal list-inside space-y-1 text-xs">
              <li>Copy your n8n webhook URL</li>
              <li>Paste it in the field above</li>
              <li>Click "Test Webhook" to verify</li>
              <li>Update your .env file with: N8N_WEBHOOK_URL=your-url</li>
              <li>Restart the application</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}