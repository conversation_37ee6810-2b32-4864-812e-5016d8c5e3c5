import { apiRequest } from "./queryClient";
import type {
  InsertFolder,
  InsertChatSession,
  InsertMessage,
  UpdateChatSession,
  Folder,
  ChatSession,
  Message,
} from "@shared/schema";

// API base path for production
const API_BASE = import.meta.env.PROD ? "/jurbot-chat/api" : "/api";

/**
 * Standard response returned by the backend when a message is sent.
 * For the n8n engine the `citations` field will be `undefined`.
 * For the RAGFlow engine the backend augments the response with an
 * optional `citations` array that contains the referenced chunks /
 * documents returned by RAGFlow.
 */
export interface ChatResponse {
  userMessage: Message;
  aiMessage: Message;
  citations?: unknown[]; // we keep it generic – UI components can refine later
}

export const chatApi = {
  // Folders
  getFolders: async (): Promise<Folder[]> => {
    const response = await apiRequest("GET", `${API_BASE}/folders`);
    return response.json();
  },

  createFolder: async (folder: InsertFolder): Promise<Folder> => {
    const response = await apiRequest("POST", `${API_BASE}/folders`, folder);
    return response.json();
  },

  updateFolder: async (id: number, folder: Partial<InsertFolder>): Promise<Folder> => {
    const response = await apiRequest("PUT", `${API_BASE}/folders/${id}`, folder);
    return response.json();
  },

  deleteFolder: async (id: number): Promise<{ success: boolean }> => {
    const response = await apiRequest("DELETE", `${API_BASE}/folders/${id}`);
    return response.json();
  },

  // Chat Sessions
  getChatSessions: async (): Promise<ChatSession[]> => {
    const response = await apiRequest("GET", `${API_BASE}/chat-sessions`);
    return response.json();
  },

  createChatSession: async (session: InsertChatSession): Promise<ChatSession> => {
    const response = await apiRequest("POST", `${API_BASE}/chat-sessions`, session);
    return response.json();
  },

  updateChatSession: async (id: string, session: UpdateChatSession): Promise<ChatSession> => {
    const response = await apiRequest("PUT", `${API_BASE}/chat-sessions/${id}`, session);
    return response.json();
  },

  deleteChatSession: async (id: string): Promise<{ success: boolean }> => {
    const response = await apiRequest("DELETE", `${API_BASE}/chat-sessions/${id}`);
    return response.json();
  },

  moveChatSession: async (sessionId: string, folderId: number | null): Promise<{ success: boolean }> => {
    const response = await apiRequest("POST", `${API_BASE}/chat-sessions/${sessionId}/move`, { folderId });
    return response.json();
  },

  // Messages
  getMessages: async (sessionId: string): Promise<Message[]> => {
    const response = await apiRequest("GET", `${API_BASE}/chat-sessions/${sessionId}/messages`);
    return response.json();
  },

  /**
   * Send a user message to the backend. The backend will forward the
   * request to the configured chat engine (n8n webhook or RAGFlow).
   *
   * The response always contains the persisted `userMessage` as well as
   * the generated `aiMessage`.  If the selected engine is RAGFlow the
   * response may additionally include a `citations` array that holds
   * metadata about the referenced document chunks so the UI can render
   * hover previews and citation lists.
   */
  sendMessage: async (sessionId: string, content: string): Promise<ChatResponse> => {
    const response = await apiRequest("POST", `${API_BASE}/chat-sessions/${sessionId}/messages`, {
      content,
      role: "user",
    });
    // The backend decides the concrete payload shape (with / without citations)
    return response.json() as Promise<ChatResponse>;
  },

  clearMessages: async (sessionId: string): Promise<{ success: boolean }> => {
    const response = await apiRequest("DELETE", `${API_BASE}/chat-sessions/${sessionId}/messages`);
    return response.json();
  },
};
