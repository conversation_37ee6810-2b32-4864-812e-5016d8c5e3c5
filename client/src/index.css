@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'Corporal';
  src: url('/assets/fonts/corporal/corporal-webfont.eot');
  src: url('/assets/fonts/corporal/corporal-webfont.eot?#iefix') format('embedded-opentype'),
       url('/assets/fonts/corporal/corporal-webfont.woff2') format('woff2'),
       url('/assets/fonts/corporal/corporal-webfont.woff') format('woff'),
       url('/assets/fonts/corporal/corporal-webfont.ttf') format('truetype'),
       url('/assets/fonts/corporal/corporal-webfont.svg#corporal') format('svg');
  font-weight: normal;
  font-style: normal;
}

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(225, 15%, 15%);
  --muted: hsl(45, 30%, 95%);
  --muted-foreground: hsl(225, 10%, 45%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(225, 15%, 15%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(225, 15%, 15%);
  --border: hsl(45, 20%, 85%);
  --input: hsl(45, 20%, 95%);
  /* Yellow primary with navy complementary colors */
  --primary: #ffe100;
  --primary-foreground: #333;
  --secondary: hsl(45, 30%, 95%);
  --secondary-foreground: hsl(225, 15%, 25%);
  --accent: #ffe100;
  --accent-foreground: #333;
  --destructive: hsl(249, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: #ffe100;
  --radius: 0.5rem;
  
  /* Custom chat colors with better contrast */
  --chat-user: hsl(0, 0%, 96%);
  --chat-user-text: hsl(0, 0%, 0%);
  --chat-ai: hsl(0, 0%, 96%);
  --chat-ai-text: hsl(0, 0%, 0%);
  --gradient-start: hsl(45, 95%, 65%);
  --gradient-end: hsl(225, 70%, 60%);
  --sidebar-bg: hsl(45, 40%, 98%);
  --chat-area-bg: hsl(45, 20%, 99%);
}

.dark {
  --background: hsl(225, 25%, 8%);
  --foreground: hsl(45, 20%, 95%);
  --muted: hsl(225, 15%, 15%);
  --muted-foreground: hsl(225, 8%, 60%);
  --popover: hsl(225, 20%, 10%);
  --popover-foreground: hsl(45, 15%, 90%);
  --card: hsl(225, 20%, 10%);
  --card-foreground: hsl(45, 15%, 90%);
  --border: hsl(225, 15%, 22%);
  --input: hsl(225, 15%, 18%);
  /* Yellow primary for dark mode */
  --primary: hsl(45, 95%, 65%);
  --primary-foreground: hsl(225, 30%, 10%);
  --secondary: hsl(225, 15%, 15%);
  --secondary-foreground: hsl(45, 15%, 85%);
  --accent: hsl(45, 95%, 65%);
  --accent-foreground: hsl(225, 30%, 10%);
  --destructive: hsl(0, 70%, 55%);
  --destructive-foreground: hsl(45, 20%, 95%);
  --ring: hsl(45, 95%, 65%);
  
  /* Custom chat colors for dark mode with better contrast */
  --chat-user: hsl(225, 20%, 14%);
  --chat-user-text: hsl(45, 95%, 95%);
  --chat-ai: hsl(225, 20%, 14%);
  --chat-ai-text: hsl(45, 15%, 85%);
  --gradient-start: hsl(45, 90%, 55%);
  --gradient-end: hsl(225, 60%, 45%);
  --sidebar-bg: hsl(225, 25%, 9%);
  --chat-area-bg: hsl(225, 20%, 12%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer utilities {
  .bg-gradient-primary {
    background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  }
  
  .chat-user-bubble {
    background-color: var(--chat-user);
    color: var(--chat-user-text);
    border: 1px solid var(--border);
  }
  
  .chat-ai-bubble {
    background-color: var(--chat-ai);
    color: var(--chat-ai-text);
    border: 1px solid var(--border);
  }
  
  .sidebar-bg {
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border);
  }
  
  .chat-area-bg {
    background-color: var(--chat-area-bg);
  }
  
  .message-input-bg {
    background-color: var(--background);
    border-top: 1px solid var(--border);
  }

  /* Custom styling for inputs in dark mode */
  .dark input[type="date"] {
    color-scheme: dark;
  }

  /* Mobile-first responsive design */
  @media (max-width: 768px) {
    .sidebar-mobile {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      width: 100vw;
      z-index: 50;
      transform: translateX(-100%);
      transition: transform 0.3s ease-in-out;
    }
    
    .sidebar-mobile.open {
      transform: translateX(0);
    }
  }
}

/* Thinking process styles */
.think-section {
  padding-left: 12px;
  color: #8b8b8b;
  border-left: 2px solid #d5d3d3;
  margin-bottom: 12px;
  font-size: 12px;
  font-style: italic;
}

.dark .think-section {
  color: rgb(166, 166, 166);
  border-left-color: rgb(78, 78, 86);
}
