import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertFolderSchema, insertChatSessionSchema, insertMessageSchema, updateChatSessionSchema } from "@shared/schema";
import { z } from "zod";
import { sendFeedbackEmail } from "./feedback";
import {                  // 🆕 RAGFlow helpers
  isRAGFlowConfigured,
  sendMessageToRAGFlow,
  configureRAGFlow,
  processStreamingResponse,
} from "./ragflow";

const N8N_WEBHOOK_URL = process.env.N8N_WEBHOOK_URL || "https://n8n.sollution.ai/webhook/e98745cf-eea7-4ee3-86ba-54wezrdth";
const CHAT_ENGINE = (process.env.CHAT_ENGINE || "n8n").toLowerCase(); // "n8n" | "ragflow"

export async function registerRoutes(app: Express): Promise<Server> {
  // Configuration endpoint
  app.get("/api/config", (req, res) => {
    res.json({
      webhookUrl: N8N_WEBHOOK_URL,
      hasWebhookConfigured: !!process.env.N8N_WEBHOOK_URL,
      chatEngine: CHAT_ENGINE,
      ragflowConfigured: isRAGFlowConfigured(),
    });
  });

  // ──────────────────────────────────────────────────────────────
  // Save / Update RAGFlow configuration
  // ──────────────────────────────────────────────────────────────
  app.post("/api/ragflow-config", async (req, res) => {
    try {
      // Validate incoming payload
      const cfg = z
        .object({
          apiKey: z.string().min(1, "apiKey is required"),
          chatId: z.string().min(1, "chatId is required"),
          serverAddress: z.string().url("serverAddress must be a valid URL"),
          sessionId: z.string().min(1, "sessionId is required"),
          enabled: z.boolean().optional(),
        })
        .parse(req.body);

      // Update in-memory configuration
      configureRAGFlow({
        apiKey: cfg.apiKey,
        chatId: cfg.chatId,
        serverAddress: cfg.serverAddress,
        sessionId: cfg.sessionId,
        enabled: cfg.enabled ?? true,
      });

      // Inform client that a restart is needed for env-based switch
      return res.json({
        success: true,
        message:
          "RAGFlow configuration updated. Please restart the server or reload environment variables for changes to take effect.",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res
          .status(400)
          .json({ success: false, error: "Invalid RAGFlow config", details: error.errors });
      }
      console.error("[ragflow-config] Error:", error);
      return res
        .status(500)
        .json({ success: false, error: "Failed to update RAGFlow configuration" });
    }
  });

  // Test webhook endpoint
  app.post("/api/test-webhook", async (req, res) => {
    try {
      const { webhookUrl, message } = req.body;
      const testUrl = webhookUrl || N8N_WEBHOOK_URL;
      const testMessage = message || "Hello, this is a test message from the chat application";
      
      console.log(`[webhook-test] ===== TEST REQUEST =====`);
      console.log(`[webhook-test] Method: POST`);
      console.log(`[webhook-test] URL: ${testUrl}`);
      console.log(`[webhook-test] Body:`, JSON.stringify({
        sessionId: "test-session",
        message: testMessage,
      }, null, 2));
      
      const testResponse = await fetch(testUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "ChatApp/1.0",
          "Accept": "application/json, text/plain, */*",
        },
        body: JSON.stringify({
          sessionId: "test-session",
          message: testMessage,
        }),
      });
      
      console.log(`[webhook-test] ===== TEST RESPONSE =====`);
      console.log(`[webhook-test] Status: ${testResponse.status} ${testResponse.statusText}`);
      console.log(`[webhook-test] Headers:`, Object.fromEntries(testResponse.headers.entries()));
      
      if (!testResponse.ok) {
        const errorText = await testResponse.text();
        console.error(`[webhook-test] Error: ${errorText}`);
        return res.status(400).json({ 
          success: false, 
          error: `Webhook test failed: ${testResponse.status} - ${errorText}` 
        });
      }
      
      const responseText = await testResponse.text();
      console.log(`[webhook-test] Raw response: ${responseText}`);
      
      let responseData: any = {};
      if (responseText.trim()) {
        try {
          responseData = JSON.parse(responseText);
        } catch (parseError) {
          responseData = { response: responseText };
        }
      }
      
      res.json({ 
        success: true, 
        message: "Webhook test successful!", 
        response: responseData,
        rawResponse: responseText 
      });
    } catch (error) {
      console.error(`[webhook-test] Error:`, error);
      res.status(500).json({ 
        success: false, 
        error: error instanceof Error ? error.message : "Unknown error" 
      });
    }
  });

  // Feedback endpoint
  app.post("/api/feedback", async (req, res) => {
    try {
      const { message } = z.object({ message: z.string().min(1).max(500) }).parse(req.body);
      await sendFeedbackEmail(message);
      res.status(200).json({ success: true, message: "Feedback sent successfully!" });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ success: false, error: "Invalid feedback data", details: error.errors });
      }
      console.error("Feedback submission error:", error);
      res.status(500).json({ success: false, error: "Failed to send feedback." });
    }
  });

  // Folders
  app.get("/api/folders", async (req, res) => {
    try {
      const folders = await storage.getFolders();
      res.json(folders);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch folders" });
    }
  });

  app.post("/api/folders", async (req, res) => {
    try {
      const folderData = insertFolderSchema.parse(req.body);
      const folder = await storage.createFolder(folderData);
      res.json(folder);
    } catch (error) {
      res.status(400).json({ message: "Invalid folder data" });
    }
  });

  app.put("/api/folders/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const folderData = insertFolderSchema.partial().parse(req.body);
      const folder = await storage.updateFolder(id, folderData);
      
      if (!folder) {
        return res.status(404).json({ message: "Folder not found" });
      }
      
      res.json(folder);
    } catch (error) {
      res.status(400).json({ message: "Invalid folder data" });
    }
  });

  app.delete("/api/folders/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteFolder(id);
      
      if (!success) {
        return res.status(404).json({ message: "Folder not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete folder" });
    }
  });

  // Chat Sessions
  app.get("/api/chat-sessions", async (req, res) => {
    try {
      const sessions = await storage.getChatSessions();
      res.json(sessions);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch chat sessions" });
    }
  });

  app.post("/api/chat-sessions", async (req, res) => {
    try {
      const sessionData = insertChatSessionSchema.parse(req.body);
      const session = await storage.createChatSession(sessionData);
      res.json(session);
    } catch (error) {
      res.status(400).json({ message: "Invalid session data" });
    }
  });

  app.put("/api/chat-sessions/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const sessionData = updateChatSessionSchema.parse(req.body);
      const session = await storage.updateChatSession(id, sessionData);
      
      if (!session) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json(session);
    } catch (error) {
      res.status(400).json({ message: "Invalid session data" });
    }
  });

  app.delete("/api/chat-sessions/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const success = await storage.deleteChatSession(id);
      
      if (!success) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete chat session" });
    }
  });

  app.post("/api/chat-sessions/:id/move", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const { folderId } = z.object({ folderId: z.number().nullable() }).parse(req.body);
      
      const success = await storage.moveChatSessionToFolder(sessionId, folderId);
      
      if (!success) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(400).json({ message: "Invalid move data" });
    }
  });

  // Messages
  app.get("/api/chat-sessions/:id/messages", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const messages = await storage.getMessages(sessionId);
      res.json(messages);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch messages" });
    }
  });

  // ──────────────────────────────────────────────────────────────
  // Send a message to a chat session (streaming)
  // ──────────────────────────────────────────────────────────────
  app.post("/api/chat-sessions/:id/messages/stream", async (req, res) => {
    const sessionId = req.params.id;
    const messageData = insertMessageSchema.parse({
      ...req.body,
      sessionId,
    });

    try {
      // Create user message
      const userMessage = await storage.createMessage({
        sessionId,
        content: messageData.content,
        role: "user",
      });

      // Set up SSE headers
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      let fullContent = '';
      let citations: any[] = [];

      if (CHAT_ENGINE === "ragflow" && isRAGFlowConfigured()) {
        try {
          const streamResponse = await sendMessageToRAGFlow(sessionId, messageData.content, true);

          await processStreamingResponse(streamResponse, (chunk) => {
            fullContent += chunk.content;
            if (chunk.citations) {
              citations = chunk.citations;
            }

            // Send chunk to client
            res.write(`data: ${JSON.stringify({
              type: 'chunk',
              content: chunk.content,
              fullContent,
              citations: chunk.citations
            })}\n\n`);
          });

          // Create final AI message
          const aiMessage = await storage.createMessage({
            sessionId,
            content: fullContent,
            role: "assistant",
          });

          // Send completion event
          res.write(`data: ${JSON.stringify({
            type: 'complete',
            userMessage,
            aiMessage,
            citations
          })}\n\n`);

        } catch (error) {
          console.error(`[ragflow] Streaming error:`, error);
          const errorMessage = error instanceof Error
            ? `AI service error: ${error.message}`
            : "I'm having trouble connecting to the AI service right now. Please try again later.";

          const aiMessage = await storage.createMessage({
            sessionId,
            content: errorMessage,
            role: "assistant",
          });

          res.write(`data: ${JSON.stringify({
            type: 'error',
            userMessage,
            aiMessage,
            error: errorMessage
          })}\n\n`);
        }
      } else {
        // Fallback to n8n (non-streaming for now)
        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: 'Streaming only supported with RAGFlow engine'
        })}\n\n`);
      }

      res.write(`data: ${JSON.stringify({ type: 'end' })}\n\n`);
      res.end();

    } catch (error) {
      console.error("Error in streaming endpoint:", error);
      res.write(`data: ${JSON.stringify({
        type: 'error',
        error: 'Internal server error'
      })}\n\n`);
      res.end();
    }
  });

  app.post("/api/chat-sessions/:id/messages", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const messageData = insertMessageSchema.parse({
        ...req.body,
        sessionId,
      });
      
      // Create user message
      const userMessage = await storage.createMessage(messageData);
      
      // === Choose chat engine ===
      if (CHAT_ENGINE === "ragflow" && isRAGFlowConfigured()) {
        /* ---------- RAGFLOW ---------- */
        try {
          const rfResult = await sendMessageToRAGFlow(sessionId, messageData.content, /* stream */ false);

          const aiMessage = await storage.createMessage({
            sessionId,
            content: rfResult.content || "",
            role: "assistant",
          });

          return res.json({ userMessage, aiMessage, citations: rfResult.citations ?? [] });
        } catch (rfError) {
          console.error(`[ragflow] Error:`, rfError);
          const errorMessage =
            rfError instanceof Error
              ? `AI service error: ${rfError.message}`
              : "I'm having trouble connecting to the AI service right now. Please try again later.";

          const aiMessage = await storage.createMessage({
            sessionId,
            content: errorMessage,
            role: "assistant",
          });

          return res.json({ userMessage, aiMessage });
        }
      } else {
        /* ---------- N8N WEBHOOK (default) ---------- */
        try {
        console.log(`[webhook] ===== WEBHOOK REQUEST =====`);
        console.log(`[webhook] Method: POST`);
        console.log(`[webhook] URL: ${N8N_WEBHOOK_URL}`);
        console.log(`[webhook] Headers:`, {
          "Content-Type": "application/json",
          "User-Agent": "ChatApp/1.0",
          "Accept": "application/json, text/plain, */*"
        });
        console.log(`[webhook] Body:`, JSON.stringify({
          sessionId,
          message: messageData.content,
        }, null, 2));
        
        const webhookResponse = await fetch(N8N_WEBHOOK_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "User-Agent": "ChatApp/1.0",
            "Accept": "application/json, text/plain, */*",
          },
          body: JSON.stringify({
            sessionId,
            message: messageData.content,
          }),
        });
        
        console.log(`[webhook] ===== WEBHOOK RESPONSE =====`);
        console.log(`[webhook] Status: ${webhookResponse.status} ${webhookResponse.statusText}`);
        console.log(`[webhook] Headers:`, Object.fromEntries(webhookResponse.headers.entries()));
        
        if (!webhookResponse.ok) {
          const errorText = await webhookResponse.text();
          console.error(`[webhook] Error response body: ${errorText}`);
          throw new Error(`Webhook failed with status ${webhookResponse.status}: ${errorText}`);
        }
        
        let webhookData: any = {};
        const responseText = await webhookResponse.text();
        console.log(`[webhook] Raw response:`, responseText);
        
        if (responseText.trim()) {
          try {
            webhookData = JSON.parse(responseText);
            console.log(`[webhook] Parsed response:`, webhookData);
          } catch (parseError) {
            console.log(`[webhook] Non-JSON response, using as plain text`);
            webhookData = { response: responseText };
          }
        }
        
        // Create AI response message
        let aiResponseContent: string;
        
        if (webhookData.output) {
          // Handle n8n response with "output" field
          aiResponseContent = webhookData.output;
        } else if (webhookData.response || webhookData.message || responseText) {
          // Handle other response formats
          aiResponseContent = webhookData.response || webhookData.message || responseText;
        } else {
          aiResponseContent = `Hello! I see you said "${messageData.content}".

⚠️ **n8n Configuration Issue**: Your webhook is receiving requests but returning empty responses. 

To fix this, check your n8n workflow:
1. Make sure the workflow is **Active** (green toggle)
2. Add a "Respond to Webhook" node at the end
3. Configure it to return: {"response": "Your AI response here"}

Once fixed, I'll provide proper AI responses!`;
        }
        
        const aiMessage = await storage.createMessage({
          sessionId,
          content: aiResponseContent,
          role: "assistant",
        });
        
        res.json({ userMessage, aiMessage });
      } catch (webhookError) {
        console.error(`[webhook] Error:`, webhookError);
        
        // Create fallback AI message with more specific error info
        const errorMessage = webhookError instanceof Error 
          ? `AI service error: ${webhookError.message}` 
          : "I'm having trouble connecting to the AI service right now. Please try again later.";
          
        const aiMessage = await storage.createMessage({
          sessionId,
          content: errorMessage,
          role: "assistant",
        });
        
        res.json({ userMessage, aiMessage });
      }
      } // end engine branch
    } catch (error) {
      res.status(400).json({ message: "Invalid message data" });
    }
  });

  app.delete("/api/chat-sessions/:id/messages", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const success = await storage.deleteMessages(sessionId);
      
      if (!success) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to clear messages" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
