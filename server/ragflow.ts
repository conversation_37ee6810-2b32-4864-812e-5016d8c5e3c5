// RAGFlow configuration interface
export interface RAGFlowConfig {
  apiKey: string;
  chatId: string;
  serverAddress: string;
  enabled: boolean;
}

// Default configuration
const defaultConfig: RAGFlowConfig = {
  apiKey: process.env.RAGFLOW_API_KEY || '',
  chatId: process.env.RAGFLOW_CHAT_ID || '',
  serverAddress: process.env.RAGFLOW_SERVER_ADDRESS || 'http://**************',
  enabled: process.env.CHAT_ENGINE === 'ragflow'
};

// Current configuration
let currentConfig: RAGFlowConfig = { ...defaultConfig };

/**
 * Configure RAGFlow with the provided settings
 * @param config RAGFlow configuration
 */
export function configureRAGFlow(config: Partial<RAGFlowConfig>): void {
  currentConfig = { ...currentConfig, ...config };
}

/**
 * Get the current RAGFlow configuration
 * @returns Current RAGFlow configuration
 */
export function getRAGFlowConfig(): RAGFlowConfig {
  return { ...currentConfig };
}

/**
 * Check if RA<PERSON><PERSON><PERSON> is properly configured
 * @returns True if RAG<PERSON>low is configured, false otherwise
 */
export function isRAGFlowConfigured(): boolean {
  return !!(currentConfig.apiKey && currentConfig.chatId && currentConfig.serverAddress);
}

/**
 * Send a message to RAGFlow and get the response
 * @param sessionId The chat session ID
 * @param message The user message
 * @param stream Whether to stream the response
 * @returns The response from RAGFlow
 */
export async function sendMessageToRAGFlow(
  sessionId: string,
  message: string,
  stream: boolean = false
): Promise<any> {
  if (!isRAGFlowConfigured()) {
    throw new Error('RAGFlow is not properly configured');
  }

  const { apiKey, chatId, serverAddress } = currentConfig;
  const url = `${serverAddress}/api/v1/chats_openai/${chatId}`;

  console.log(`[ragflow] ===== RAGFLOW REQUEST =====`);
  console.log(`[ragflow] Method: POST`);
  console.log(`[ragflow] URL: ${url}`);
  console.log(`[ragflow] Headers:`, {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`,
    'User-Agent': 'ChatApp/1.0',
    'Accept': 'application/json, text/plain, */*'
  });
  console.log(`[ragflow] Body:`, JSON.stringify({
    model: 'model',
    messages: [
      { role: 'system', content: 'You are a helpful assistant.' },
      { role: 'user', content: message }
    ],
    stream
  }, null, 2));

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'User-Agent': 'ChatApp/1.0',
        'Accept': 'application/json, text/plain, */*'
      },
      body: JSON.stringify({
        model: 'model',
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: message }
        ],
        stream
      })
    });

    console.log(`[ragflow] ===== RAGFLOW RESPONSE =====`);
    console.log(`[ragflow] Status: ${response.status} ${response.statusText}`);
    console.log(`[ragflow] Headers:`, Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[ragflow] Error response body: ${errorText}`);
      throw new Error(`RAGFlow API failed with status ${response.status}: ${errorText}`);
    }

    if (stream) {
      // Return the response directly for streaming
      return response;
    } else {
      const data = await response.json();
      console.log(`[ragflow] Parsed response:`, data);
      
      // Extract the assistant's message
      const assistantMessage = data.choices[0].message.content;
      
      // Check if there are citations in the response
      const citations = data.choices[0].message.reference || [];
      
      return {
        content: assistantMessage,
        citations
      };
    }
  } catch (error) {
    console.error(`[ragflow] Error:`, error);
    throw error;
  }
}

/**
 * Process a streaming response from RAGFlow
 * @param response The streaming response from RAGFlow
 * @param onChunk Callback function for each chunk
 */
export async function processStreamingResponse(
  response: Response,
  onChunk: (chunk: { content: string, citations?: any[] }) => void
): Promise<void> {
  if (!response.body) {
    throw new Error('Response body is null');
  }

  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  let buffer = '';
  let citations: any[] = [];

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      
      // Process complete data chunks
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() === '') continue;
        if (line.trim() === 'data: [DONE]') continue;

        try {
          const dataMatch = line.match(/^data: (.+)$/);
          if (!dataMatch) continue;
          
          const data = JSON.parse(dataMatch[1]);
          
          // Extract content from the chunk
          const content = data.choices[0]?.delta?.content || '';
          
          // Extract citations if available
          if (data.choices[0]?.delta?.reference) {
            citations = data.choices[0].delta.reference;
          }
          
          // Call the callback with the chunk data
          onChunk({ content, citations: citations.length > 0 ? citations : undefined });
        } catch (e) {
          console.error('Error parsing streaming data:', e);
        }
      }
    }
    
    // Process any remaining data
    if (buffer.trim()) {
      try {
        const dataMatch = buffer.match(/^data: (.+)$/);
        if (dataMatch) {
          const data = JSON.parse(dataMatch[1]);
          const content = data.choices[0]?.delta?.content || '';
          
          if (data.choices[0]?.delta?.reference) {
            citations = data.choices[0].delta.reference;
          }
          
          onChunk({ content, citations: citations.length > 0 ? citations : undefined });
        }
      } catch (e) {
        console.error('Error parsing final streaming data:', e);
      }
    }
  } catch (error) {
    console.error('Error processing streaming response:', error);
    throw error;
  }
}
