.image {
  width: 100px !important;
  object-fit: contain;
}

.imagePreview {
  max-width: 50vw;
  max-height: 50vh;
  object-fit: contain;
}

.content {
  flex: 1;
  .chunkText;
}

.contentEllipsis {
  .multipleLineEllipsis(3);
}

.contentText {
  word-break: break-all !important;
}

.chunkCard {
  width: 100%;
  padding: 18px 10px;
}

.cardSelected {
  background-color: @selectedBackgroundColor;
}

.cardSelectedDark {
  background-color: #ffffff2f;
}
