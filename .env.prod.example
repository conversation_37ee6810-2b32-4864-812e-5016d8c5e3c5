# N8N Webhook Configuration
N8N_WEBHOOK_URL=https://n8n.sollution.ai/webhook/e98745cf-eea7-4ee3-86ba-54wezrdth

# Chat Engine Selection
# Options: 'n8n' (default) or 'ragflow'
CHAT_ENGINE=n8n

# RAGFlow Configuration (uncomment and fill when CHAT_ENGINE=ragflow)
# RAGFLOW_API_KEY=ragflow-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# RAGFLOW_CHAT_ID=00000000000000000000000000000000
# RAGFLOW_SERVER_ADDRESS=http://your-ragflow-server-address

# Server Configuration
PORT=5000
NODE_ENV=production

# AWS SES Configuration for Feedback Feature
AWS_REGION=eu-central-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=3maibc49P8Omx5XW6HcXEdWcqCIkBE1OpwsB8ls6
AWS_SES_FROM_EMAIL=<EMAIL>

# Secret key for signing JWTs. Must match the secret key in your Django/Wagtail backend.
JWT_SECRET="uTeebeekooPi3EabiKaibea7ohyuongoxeike5eenah9ri9OyooD4sheReeceith"

# The full URL to redirect to on logout in production.
VITE_LOGOUT_URL="https://strafrecht-online.org/login"

# Database credentials
POSTGRES_USER="db_user"
POSTGRES_PASSWORD="db_PassW0rd"
POSTGRES_DB="db_jurbot"